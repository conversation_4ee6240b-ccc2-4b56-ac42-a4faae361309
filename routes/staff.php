<?php

use App\Http\Controllers\Staff\CalendarController;
use App\Http\Controllers\Staff\CustomerController;
use App\Http\Controllers\Staff\DashboardController;
use App\Http\Controllers\Staff\DocumentController;
use App\Http\Controllers\Staff\ImageController;
use App\Http\Controllers\Staff\LoginController;
use App\Http\Controllers\Staff\MeController;
use App\Http\Controllers\Staff\NotificationController;
use App\Http\Controllers\Staff\OrderStockInController;
use App\Http\Controllers\Staff\OrderStockInPutAwayController;
use App\Http\Controllers\Staff\OrderStockInQcController;
use App\Http\Controllers\Staff\OrderStockOutCollectController;
use App\Http\Controllers\Staff\OrderStockOutController;
use App\Http\Controllers\Staff\OrderStockOutPickingController;
use App\Http\Controllers\Staff\ProductController;
use App\Http\Controllers\Staff\WarehouseController;
use App\Http\Controllers\Staff\WarehouseRackController;
use App\Http\Controllers\Staff\WarehouseRackProductController;
use App\Http\Controllers\Staff\ProductOrderController;
use Illuminate\Support\Facades\Route;

Route::controller(LoginController::class)->group(function () {
    Route::get('login', 'index')->name('.login')->middleware('guest:staff');
    Route::post('login', 'authenticate')->name('.login')->middleware('guest:staff');
    Route::any('logout', 'logout')->name('.logout')->middleware('auth:staff');
});

Route::middleware(['auth:staff'])->scopeBindings()->group(function () {
    Route::get('', [DashboardController::class, 'switch'])->name('.switch');

    Route::group(['prefix' => 'me', 'as' => '.me'], function () {
        Route::get('edit', [MeController::class, 'edit'])->name('.edit');
        Route::post('update', [MeController::class, 'update'])->name('.update');
        Route::get('edit-password', [MeController::class, 'editPassword'])->name('.edit-password');
        Route::post('update-password', [MeController::class, 'updatePassword'])->name('.update-password');
    });

    Route::prefix('notifications')->name('.notification')->controller(NotificationController::class)->group(function () {
        Route::get('index', 'index');
        Route::post('query', 'query')->name('.query');
        Route::post('mark-read', [NotificationController::class, 'markReadNRedirect'])->name('.notification-read');
        Route::post('all-mark-read', [NotificationController::class, 'markAllRead'])->name('.notification-all-read');
    });

    Route::prefix('{warehouse:slug}')->group(function () {
        Route::get('show', [WarehouseController::class, 'show'])->name('.show');

        Route::get('edit', [WarehouseController::class, 'edit'])->name('.edit');
        Route::post('', [WarehouseController::class, 'update'])->name('.update');
        Route::get('get-customers', [WarehouseController::class, 'getCustomers'])->name('.get-customers');

        Route::prefix('zones')->name('.warehouse.zone')->controller(WarehouseController::class)->group(function () {
            Route::get('', 'zones');
            Route::post('query', 'zonesQuery')->name('.query');
        });

        Route::prefix('racks')->name('.warehouse.rack')->group(function () {
            Route::get('', [WarehouseController::class, 'racks']);
            Route::post('query', [WarehouseController::class, 'racksQuery'])->name('.query');

            // New rack routes
            Route::get('create', [WarehouseRackController::class, 'create'])->name('.create');
            Route::post('', [WarehouseRackController::class, 'store'])->name('.store');
            Route::get('{rack}/edit', [WarehouseRackController::class, 'edit'])->name('.edit');
            Route::post('{rack}', [WarehouseRackController::class, 'update'])->name('.update');
            Route::delete('{rack}', [WarehouseRackController::class, 'delete'])->name('.delete');
            Route::get('{rack}/qr-code', [WarehouseRackController::class, 'showQrCode'])->name('.show-qr');

            Route::prefix('{rack}/products')->name('.product')->controller(WarehouseRackProductController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });
        });

        Route::prefix('calendar')->name('.calendar')->controller(CalendarController::class)->group(function () {
            Route::get('', 'index');
            Route::get('event', 'event')->name('.event');
        });

        Route::prefix('images')->name('.image')->controller(ImageController::class)->group(function () {
            Route::prefix('dropzone')->name('.dropzone')->group(function () {
                Route::get('', 'dropzone');
                Route::post('', 'upload')->name('.upload');
            });

            Route::delete('{image}', 'delete')->name('.delete');

            Route::post('ckeditor-simple-upload', 'ckeditorSimpleUpload')->name('.ckeditor-simple-upload');
        });

        Route::prefix('customers')->name('.customer')->controller(CustomerController::class)->group(function () {
            Route::get('', 'index');
            // Route::get('create', 'create')->name('.create');
            Route::get('{warehouse_customer}', 'show')->name('.show');
            Route::post('query', 'query')->name('.query');
            Route::post('', 'store')->name('.store');
            Route::get('{warehouse_customer}/edit', 'edit')->name('.edit');
            Route::post('{warehouse_customer}', 'update')->name('.update');
            Route::delete('{warehouse_customer}', 'delete')->name('.delete');
        });

        Route::prefix('products')->name('.product')->controller(ProductController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::post('update-sequence', 'updateSequence')->name('.update-sequence');
            Route::get('export', 'export')->name('.export');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{customer_product}/edit', 'edit')->name('.edit');
            Route::post('{customer_product}', 'update')->name('.update');
            Route::delete('{customer_product}', 'delete')->name('.delete');

            Route::prefix('{customer_product}/orders')->name('.order')->controller(ProductOrderController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });
        });

        Route::prefix('stock-in')->name('.stock-in')->controller(OrderStockInController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{order}', 'show')->name('.show');
            Route::get('{order}/edit', 'edit')->name('.edit');
            Route::post('{order}/update', 'update')->name('.update');
            Route::get('get-warehouse-customer-products/{warehouse_customer_id}', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
            Route::post('{order}/update-status', 'updateStatus')->name('.update-status');
            Route::get('{order}/qr-download/{type}', 'downloadQrCode')->name('.qr-download');
            Route::get('{order}/qr-image/{size}', 'displayQrImage')->name('.qr-image');

            Route::prefix('{order}/qc')->name('.qc')->controller(OrderStockInQcController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });

            Route::prefix('{order}/put-away')->name('.put-away')->controller(OrderStockInPutAwayController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });
        });

        Route::prefix('stock-out')->name('.stock-out')->controller(OrderStockOutController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{order}', 'show')->name('.show');
            Route::get('{order}/edit', 'edit')->name('.edit');
            Route::put('{order}/update', 'update')->name('.update'); // Changed from POST to PUT
            Route::get('get-warehouse-customer-products/{warehouse_customer_id}', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
            Route::post('{order}/update-status', 'updateStatus')->name('.update-status');
            Route::post('{order}/generate-delivery-order', 'generateDeliveryOrder')->name('.generate-delivery-order');
            Route::get('{order}/qr-download/{type}', 'downloadQrCode')->name('.qr-download');
            Route::get('{order}/qr-image/{size}', 'displayQrImage')->name('.qr-image');

            Route::prefix('{order}/picking')->name('.picking')->controller(OrderStockOutPickingController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });

            Route::prefix('{order}/collect')->name('.collect')->controller(OrderStockOutCollectController::class)->group(function () {
                Route::get('', 'index');
            });
        });

        Route::prefix('documents')->name('.document')->controller(DocumentController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('/get-warehouse-racks/{warehouse_id}', 'getWarehouseRacks')->name('.get-warehouse-racks');
            Route::get('/get-warehouse-products/{warehouse_id}', 'getWarehouseProducts')->name('.get-warehouse-products');
            Route::get('/get-warehouse-rack-products/{warehouse_rack_id}', 'getWarehouseRackProducts')->name('.get-warehouse-rack-products');
            Route::get('excel-report/{document_id}', 'excelReport')->name('.download-excel-report');
            Route::get('cargo-report', 'cargoReport')->name('.cargo-report');
            Route::post('generate-cargo-report', 'generateCargoReport')->name('.generate-cargo-report');
        });
    });
});
