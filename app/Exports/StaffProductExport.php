<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class StaffProductExport implements FromCollection, WithHeadings, WithMapping
{
    protected $products;

    public function __construct($products)
    {
        $this->products = $products;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->products;
    }

    public function headings(): array
    {
        return [
            'Sequence',
            'SKU Code',
            'SKU Name',
            'Customer',
            'UOM',
            'Cubage (m³)',
            'Weight (kg)',
            'Total Cubage (m³)',
            'Total Weight (kg)',
            'Total Available Stock',
            'Status',
            'Created At',
        ];
    }

    public function map($product): array
    {
        return [
            $product->sequence ?? '-',
            $product->sku,
            $product->name,
            $product->warehouseCustomer->name ?? '-',
            $product->uomType->name ?? '-',
            $product->cubage ?? '-',
            $product->weight ?? '-',
            $product->productStocks->sum(function($stock) {
                return $stock->quantity * ($stock->customerProduct->cubage ?? 0);
            }),
            $product->productStocks->sum(function($stock) {
                return $stock->quantity * ($stock->customerProduct->weight ?? 0);
            }),
            $product->productStocks->sum('quantity'),
            $product->is_active ? 'Active' : 'Inactive',
            $product->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
