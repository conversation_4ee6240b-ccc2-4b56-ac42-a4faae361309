<?php

namespace App\Models;

use App\Library\Traits\HasModelKit;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class WarehouseCustomerProduct extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;
    use HasModelKit;

    protected $fillable = [
        'name',
        'sku',
        'cubage',
        'weight',
        'description',
        'is_active',
        'warehouse_uom_type_id',
        'sequence',
    ];

    protected function image(): Attribute
    {
        return Attribute::make(
            get: fn ($image) => $image ?? asset('/dashboard-assets/media/avatars/sku-blank.png'),
            set: fn ($image) => $image
        );
    }

    public function warehouseCustomer()
    {
        return $this->belongsTo(WarehouseCustomer::class);
    }

    public function category(): HasOneThrough
    {
        return $this->hasOneThrough(WarehouseProductCategory::class, CustomerProductCategoryMapping::class, 'warehouse_customer_product_id', 'id', 'id', 'warehouse_product_category_id');
    }

    public function categoryMappings(): HasMany
    {
        return $this->HasMany(CustomerProductCategoryMapping::class);
    }

    public function uomType(): BelongsTo
    {
        return $this->belongsTo(WarehouseUomType::class, 'warehouse_uom_type_id');
    }

    public function warehouse(): HasOneThrough
    {
        return $this->hasOneThrough(Warehouse::class, WarehouseCustomer::class, 'id', 'id', 'warehouse_customer_id', 'warehouse_id');
    }

    public function productStocks(): HasMany
    {
        return $this->hasMany(WarehouseRackProductStock::class);
    }

    public function orderItems(): HasMany
    {
        return $this->HasMany(OrderItem::class);
    }
}
