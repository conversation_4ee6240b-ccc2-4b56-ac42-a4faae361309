<?php

namespace App\Http\Controllers\Staff;

use App\Models\CustomerProductCategoryMapping;
use App\Models\Warehouse;
use App\Models\WarehouseCustomer;
use App\Models\WarehouseCustomerProduct;
use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StaffProductExport;

class ProductController extends Controller
{
    public function index(Warehouse $warehouse)
    {
        $warehouse_customer_dropdown = $warehouse->warehouseCustomers->pluck('name','id');

        return view('staff.product.index', compact('warehouse', 'warehouse_customer_dropdown'));
    }

    public function query(Request $request, Warehouse $warehouse)
    {
        $warehouse_customer = $request->input('warehouse_customer');

        $query = $warehouse->customerProducts()->select('warehouse_customer_products.*')
                ->when($warehouse_customer !== null, function ($q) use ($warehouse_customer) {
                    $q->where('warehouse_customer_id', $warehouse_customer);
                })
                ->orderByRaw('sequence IS NULL, sequence ASC');

        $result = DataTables::of($query)
            ->addColumn('image', function ($row) {
                return '<div class="symbol symbol-50px"><img src="' . $row->image . '" alt="SKU Image" class="w-100"></div>';
            })
            ->editColumn('is_active', function ($row) {
                return $row->is_active ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-danger">Inactive</span>';
            })
            ->addColumn('customer', function ($row) {
                return $row->warehouseCustomer->name;
            })
            ->addColumn('uomType.name', function ($row) {
                return $row->uomType->name ?? '-';
            })
            ->addColumn('total_stock', function ($row) {
                return $row->productStocks->sum('quantity') ?? '-';
            })
            ->addColumn('total_cubage', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->cubage ? number_format($totalStock * $row->cubage, 3) . ' m³' : '-';
            })
            ->addColumn('total_weight', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->weight ? number_format($totalStock * $row->weight, 2) . ' kg' : '-';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at;
            })
            ->addColumn('actions', function ($row) use ($warehouse) {
                $actions = '<div class="d-flex flex-column justify-content-start">';
                $actions .= ' <a href="' . route('staff.product.edit', [$warehouse, $row->id]) . '" class="btn btn-sm btn-success mb-2">Edit</a>';
                $actions .= ' <a href="' . route('staff.product.order', [$warehouse, $row->id]) . '" class="btn btn-sm btn-info">View</a>';
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['image', 'is_active', 'actions'])->make(true);

        return $result;
    }

    public function create(Warehouse $warehouse)
    {
        return view('staff.product.create', compact('warehouse'));
    }

    public function store(Photoshop $photoshop, Request $request, Warehouse $warehouse)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'sku' => 'required|unique:warehouse_customer_products,sku',
            'weight' => 'nullable|numeric|min:0',
            'cubage' => 'nullable|numeric|min:0',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'warehouse_uom_type_id' => 'required|exists:warehouse_uom_types,id',
            'warehouse_product_category_id' => 'required|exists:warehouse_product_categories,id',
        ])->validate();

        $customer_product = new WarehouseCustomerProduct;
        $customer_product->fill($request->all());
        $customer_product->warehouse_customer_id = $request->get('warehouse_customer_id');
        $customer_product->warehouse_uom_type_id = $request->get('warehouse_uom_type_id');

        if ($request->hasFile('image')) {
            $filename = $request->file('image')->hashName('products');
            $resized = $photoshop->take($request->file('image'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $customer_product->image = asset('storage/' . $filename);
        }

        $customer_product->save();

        $categoryMapping = new CustomerProductCategoryMapping;
        $categoryMapping->warehouse_customer_product_id = $customer_product->id;
        $categoryMapping->warehouse_product_category_id = $request->get('warehouse_product_category_id');
        $categoryMapping->save();

        Session::flash('alert-success', 'Successfully Assigned');

        return redirect()->route('staff.product', $warehouse);
    }

    public function edit(Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        return view('staff.product.edit', compact(['warehouse', 'customer_product']));
    }

    public function update(Photoshop $photoshop, Request $request, Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'sku' => 'required|unique:warehouse_customer_products,sku,' . $customer_product->id,
            'weight' => 'nullable|numeric|min:0',
            'cubage' => 'nullable|numeric|min:0',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'warehouse_uom_type_id' => 'required|exists:warehouse_uom_types,id',
            'warehouse_product_category_id' => 'required|exists:warehouse_product_categories,id',
        ])->validate();

        $customer_product->fill($request->all());
        $customer_product->warehouse_customer_id = $request->get('warehouse_customer_id');

        if ($request->hasFile('image')) {
            $filename = $request->file('image')->hashName('products');
            $resized = $photoshop->take($request->file('image'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $customer_product->image = asset('storage/' . $filename);
        }

        $customer_product->save();

        $categoryMapping = new CustomerProductCategoryMapping;
        $categoryMapping->warehouse_customer_product_id = $customer_product->id;
        $categoryMapping->warehouse_product_category_id = $request->get('warehouse_product_category_id');
        $categoryMapping->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('staff.product', $warehouse);
    }

    public function delete(Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        if ($customer_product->orderItems()->count() > 0) {
            Session::flash('alert-danger', 'Failed to delete. This product has been associated with orders.');

            return redirect()->back();
        }

        $customer_product->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('staff.product', $warehouse);
    }

    public function updateSequence(Request $request, Warehouse $warehouse)
    {
        $validator = Validator::make($request->all(), [
            'sequences' => 'required|array',
            'sequences.*.id' => 'required|exists:warehouse_customer_products,id',
            'sequences.*.sequence' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Invalid data provided.'], 400);
        }

        try {
            foreach ($request->sequences as $item) {
                WarehouseCustomerProduct::where('id', $item['id'])
                    ->update(['sequence' => $item['sequence']]);
            }

            return response()->json(['success' => true, 'message' => 'Sequence updated successfully.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update sequence.'], 500);
        }
    }

    public function export(Request $request, Warehouse $warehouse)
    {
        $warehouse_customer = $request->input('warehouse_customer');

        $query = $warehouse->customerProducts()
            ->when($warehouse_customer !== null, function ($q) use ($warehouse_customer) {
                $q->where('warehouse_customer_id', $warehouse_customer);
            })
            ->orderByRaw('sequence IS NULL, sequence ASC')
            ->get();

        return Excel::download(new StaffProductExport($query), 'products_' . $warehouse->name . '_' . now()->format('Y-m-d_H-i-s') . '.xlsx');
    }
}
