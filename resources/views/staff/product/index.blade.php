@extends('staff.layouts.index')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">SKUs</h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('staff.switch') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                SKUs
                            </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        <a href="{{ route('staff.product.export', $warehouse) }}" class="btn btn-sm btn-flex btn-success" id="exportBtn">
                            <i class="ki-outline ki-file-down fs-4"></i>Export Excel
                        </a>
                        <a href="{{ route('staff.product.create', $warehouse) }}" class="btn btn-sm btn-flex btn-primary">
                            <i class="ki-outline ki-plus fs-4"></i>Create New SKU
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-12 col-md-4 col-lg-4 mb-3">
                                {!! html()->label('Warehouse company')->class('form-label') !!}
                                <div class="input-group">
                                    {!! html()->select('warehouse_cust', $warehouse_customer_dropdown , null)->class('form-control' . $errors->first('warehouse_cust', ' is-invalid'))->placeholder('Please select a warehouse customer')->data('control', 'select2')->id('warehouse-cust-select') !!}
                                    <div class="invalid-feedback">
                                        @error('warehouse_cust')
                                            {{ $message }}
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive" style="min-width: 1200px; overflow-x: auto;">
                            <table id="dataTable" class="table table-row-dashed dataTable no-footer" style="width: 100%;">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th style="white-space: nowrap;">Image</th>
                                        <th style="white-space: nowrap;">SKU Code</th>
                                        <th style="white-space: nowrap;">SKU Name</th>
                                        <th style="white-space: nowrap;">Warehouse Customer</th>
                                        <th style="white-space: nowrap;">UOM</th>
                                        <th style="white-space: nowrap;">Total Cubage (m³)</th>
                                        <th style="white-space: nowrap;">Total Weight (kg)</th>
                                        <th style="white-space: nowrap;">Total Available Stock</th>
                                        <th style="white-space: nowrap;">Status</th>
                                        <th style="white-space: nowrap;">Sequence</th>
                                        <th style="white-space: nowrap;">Created at</th>
                                        <th style="white-space: nowrap;">Actions</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#dataTable').DataTable({
            ajax: {
                url: "{{ route('staff.product.query', $warehouse) }}",
                method: "POST",
                data: function(d) {
                    d.warehouse_customer = $('#warehouse-cust-select').val();

                }
            },
            serverSide: true,
            order: [
                [9, 'desc']
            ],
            bFilter: true,
            scrollX: true,
            scrollCollapse: true,
            autoWidth: false,
            responsive: {
                details: {
                    type: 'column',
                    target: 'tr'
                }
            },
            columnDefs: [
                {
                    targets: 0,
                    className: 'control-disable dt-body-center'
                }
            ],
            ordering: false,
            rowReorder: {
                selector: 'td:nth-child(10)', // Sequence column
                update: false
            },
            columns: [{
                    data: "image",
                    orderable: false,
                    searchable: false,
                    width: "70px",
                    className: 'dt-body-center'
                },
                {
                    data: "sku",
                    orderable: false,
                    width: "150px",
                },
                {
                    data: "name",
                    orderable: false,
                    width: "200px"
                },
                {
                    data: "customer",
                    orderable: false,
                    width: "150px",
                },
                {
                    data: "uomType.name",
                    orderable: false,
                    width: "80px",
                },
                {
                    data: "total_cubage",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "total_weight",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "total_stock",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "is_active",
                    orderable: false,
                    searchable: false,
                    width: "80px",
                },
                {
                    data: "sequence",
                    orderable: false,
                    searchable: false,
                    width: "80px",
                    className: 'reorder'
                },
                {
                    data: "created_at",
                    searchable: false,
                    width: "120px",
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false,
                    width: "100px",
                }
            ]
        });

        $(document).on('change', '#warehouse-cust-select', function(){
            oTable.ajax.reload();
        });

        // Handle row reordering
        oTable.on('row-reorder', function (e, diff, edit) {
            if (diff.length === 0) {
                return;
            }

            let sequences = [];
            for (let i = 0; i < diff.length; i++) {
                let rowData = oTable.row(diff[i].node).data();
                sequences.push({
                    id: rowData.id,
                    sequence: diff[i].newPosition + 1
                });
            }

            // Update sequences via AJAX
            $.ajax({
                url: "{{ route('staff.product.update-sequence', $warehouse) }}",
                method: 'POST',
                data: {
                    sequences: sequences,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Sequence updated successfully');
                        oTable.ajax.reload();
                    } else {
                        toastr.error('Failed to update sequence');
                    }
                },
                error: function() {
                    toastr.error('Failed to update sequence');
                }
            });
        });

        // Handle export with current filter
        $('#exportBtn').on('click', function(e) {
            e.preventDefault();
            let warehouseCustomer = $('#warehouse-cust-select').val();
            let exportUrl = "{{ route('staff.product.export', $warehouse) }}";

            if (warehouseCustomer) {
                exportUrl += '?warehouse_customer=' + warehouseCustomer;
            }

            window.location.href = exportUrl;
        })
    </script>
@endpush
